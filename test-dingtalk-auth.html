<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钉钉认证测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .user-info {
            background-color: #e7f3ff;
            border: 1px solid #b3d7ff;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>钉钉认证测试页面</h1>
        
        <div id="status" class="status info">
            正在初始化...
        </div>

        <div class="controls">
            <button onclick="testCookieCheck()">检查Cookie</button>
            <button onclick="testAPICall()">测试API调用</button>
            <button onclick="testFullAuth()">完整认证测试</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div id="userInfo" class="user-info" style="display: none;">
            <h3>用户信息</h3>
            <div id="userDetails"></div>
        </div>

        <div class="log" id="log"></div>
    </div>

    <script>
        // 模拟Chrome扩展的钉钉认证管理器
        class DingTalkAuthTester {
            constructor() {
                this.isAuthenticated = false;
                this.userInfo = null;
                this.log('钉钉认证测试器初始化完成');
            }

            log(message) {
                const timestamp = new Date().toLocaleTimeString();
                const logElement = document.getElementById('log');
                logElement.innerHTML += `[${timestamp}] ${message}\n`;
                logElement.scrollTop = logElement.scrollHeight;
                console.log(`[DingTalk Auth] ${message}`);
            }

            updateStatus(message, type = 'info') {
                const statusElement = document.getElementById('status');
                statusElement.textContent = message;
                statusElement.className = `status ${type}`;
            }

            // 模拟检查Cookie的功能
            async checkValidCookie() {
                this.log('开始检查钉钉Cookie...');
                
                try {
                    // 检查当前域名是否为钉钉相关域名
                    const currentDomain = window.location.hostname;
                    this.log(`当前域名: ${currentDomain}`);
                    
                    if (currentDomain.includes('dingtalk.com')) {
                        this.log('检测到钉钉域名，模拟Cookie检查...');
                        
                        // 模拟检查Cookie
                        const cookies = document.cookie;
                        this.log(`当前Cookie: ${cookies || '无Cookie'}`);
                        
                        // 简单模拟：如果有任何Cookie就认为有效
                        const hasValidCookie = cookies.length > 0;
                        this.log(`Cookie检查结果: ${hasValidCookie ? '有效' : '无效'}`);
                        
                        return hasValidCookie;
                    } else {
                        this.log('非钉钉域名，无法检查Cookie');
                        return false;
                    }
                } catch (error) {
                    this.log(`Cookie检查失败: ${error.message}`);
                    return false;
                }
            }

            // 模拟API调用
            async makeSecureRequest(url) {
                this.log(`发起API请求: ${url}`);
                
                try {
                    const response = await fetch(url, {
                        method: 'GET',
                        credentials: 'include',
                        headers: {
                            'Accept': 'application/json',
                            'Content-Type': 'application/json'
                        }
                    });

                    this.log(`API响应状态: ${response.status} ${response.statusText}`);
                    
                    if (response.ok) {
                        const data = await response.json();
                        this.log(`API响应数据: ${JSON.stringify(data, null, 2)}`);
                        return data;
                    } else {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                } catch (error) {
                    this.log(`API请求失败: ${error.message}`);
                    throw error;
                }
            }

            // 测试用户信息获取
            async fetchUserInfoFromAPI() {
                const baseUrl = 'https://docs.dingtalk.com';
                const apiEndpoints = [
                    '/api/users/getUserInfo',
                    '/api/user/info',
                    '/api/v1/user/info'
                ];

                for (const endpoint of apiEndpoints) {
                    try {
                        this.log(`尝试API端点: ${endpoint}`);
                        const response = await this.makeSecureRequest(`${baseUrl}${endpoint}`);
                        
                        if (response && (response.data || response.result || response.name || response.userId)) {
                            this.log(`API端点 ${endpoint} 返回有效数据`);
                            
                            const userData = response.data || response.result || response;
                            return {
                                name: userData.name || userData.nick || userData.nickname || userData.userName || '钉钉用户',
                                userId: userData.userId || userData.uid || userData.id || `dingtalk_user_${Date.now()}`,
                                avatar: userData.avatar || userData.avatarUrl || userData.photo || '',
                                email: userData.email || userData.mail || '',
                                mobile: userData.mobile || userData.phone || '',
                                loginTime: new Date().toLocaleString(),
                                source: 'api_auth'
                            };
                        }
                        
                    } catch (error) {
                        this.log(`API端点 ${endpoint} 调用失败: ${error.message}`);
                        continue;
                    }
                }

                this.log('所有API端点都调用失败');
                return null;
            }

            // 完整认证流程测试
            async testFullAuthentication() {
                this.log('开始完整认证流程测试...');
                this.updateStatus('正在进行认证测试...', 'info');

                try {
                    // 1. 检查Cookie
                    const hasValidCookie = await this.checkValidCookie();
                    
                    if (hasValidCookie) {
                        this.log('检测到有效Cookie，尝试获取用户信息');
                        
                        // 2. 尝试API获取用户信息
                        try {
                            const userInfo = await this.fetchUserInfoFromAPI();
                            if (userInfo) {
                                this.isAuthenticated = true;
                                this.userInfo = userInfo;
                                this.log('成功通过API获取用户信息');
                                this.updateStatus('认证成功 - 通过API获取用户信息', 'success');
                                this.displayUserInfo(userInfo);
                                return;
                            }
                        } catch (apiError) {
                            this.log(`API获取用户信息失败: ${apiError.message}`);
                        }

                        // 3. 使用基础认证状态
                        this.log('使用基础认证状态');
                        this.isAuthenticated = true;
                        this.userInfo = {
                            name: '钉钉用户',
                            userId: `dingtalk_user_${Date.now()}`,
                            avatar: '',
                            email: '',
                            loginTime: new Date().toLocaleString(),
                            source: 'cookie_auth'
                        };
                        
                        this.updateStatus('认证成功 - 使用基础认证状态', 'success');
                        this.displayUserInfo(this.userInfo);
                        
                    } else {
                        this.log('未检测到有效Cookie，用户未登录');
                        this.isAuthenticated = false;
                        this.userInfo = null;
                        this.updateStatus('认证失败 - 未检测到有效Cookie', 'error');
                        this.hideUserInfo();
                    }

                } catch (error) {
                    this.log(`认证过程中发生错误: ${error.message}`);
                    this.updateStatus(`认证失败 - ${error.message}`, 'error');
                    this.hideUserInfo();
                }
            }

            displayUserInfo(userInfo) {
                const userInfoElement = document.getElementById('userInfo');
                const userDetailsElement = document.getElementById('userDetails');
                
                userDetailsElement.innerHTML = `
                    <p><strong>姓名:</strong> ${userInfo.name}</p>
                    <p><strong>用户ID:</strong> ${userInfo.userId}</p>
                    <p><strong>头像:</strong> ${userInfo.avatar || '无'}</p>
                    <p><strong>邮箱:</strong> ${userInfo.email || '无'}</p>
                    <p><strong>手机:</strong> ${userInfo.mobile || '无'}</p>
                    <p><strong>登录时间:</strong> ${userInfo.loginTime}</p>
                    <p><strong>认证来源:</strong> ${userInfo.source}</p>
                `;
                
                userInfoElement.style.display = 'block';
            }

            hideUserInfo() {
                const userInfoElement = document.getElementById('userInfo');
                userInfoElement.style.display = 'none';
            }
        }

        // 初始化测试器
        const authTester = new DingTalkAuthTester();

        // 测试函数
        async function testCookieCheck() {
            authTester.log('=== 开始Cookie检查测试 ===');
            const result = await authTester.checkValidCookie();
            authTester.log(`Cookie检查结果: ${result}`);
            authTester.log('=== Cookie检查测试完成 ===');
        }

        async function testAPICall() {
            authTester.log('=== 开始API调用测试 ===');
            try {
                const userInfo = await authTester.fetchUserInfoFromAPI();
                if (userInfo) {
                    authTester.log('API调用成功，获取到用户信息');
                    authTester.displayUserInfo(userInfo);
                } else {
                    authTester.log('API调用失败，未获取到用户信息');
                }
            } catch (error) {
                authTester.log(`API调用测试失败: ${error.message}`);
            }
            authTester.log('=== API调用测试完成 ===');
        }

        async function testFullAuth() {
            authTester.log('=== 开始完整认证测试 ===');
            await authTester.testFullAuthentication();
            authTester.log('=== 完整认证测试完成 ===');
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 页面加载完成后自动进行初始测试
        window.addEventListener('load', () => {
            authTester.updateStatus('测试器已准备就绪，请点击按钮进行测试', 'info');
        });
    </script>
</body>
</html>
