// 测试钉钉认证扩展的脚本
// 这个脚本可以在浏览器控制台中运行来测试扩展功能

console.log('=== 钉钉认证扩展测试开始 ===');

// 模拟扩展的钉钉认证管理器
class DingTalkAuthTester {
  constructor() {
    this.isAuthenticated = false;
    this.userInfo = null;
    this.baseUrl = 'https://docs.dingtalk.com';
  }

  log(message) {
    const timestamp = new Date().toLocaleTimeString();
    console.log(`[${timestamp}] ${message}`);
  }

  // 检查当前域名是否为钉钉域名
  isDingTalkDomain() {
    const hostname = window.location.hostname;
    const dingTalkDomains = [
      'dingtalk.com',
      'docs.dingtalk.com',
      'www.dingtalk.com'
    ];
    
    return dingTalkDomains.some(domain => 
      hostname === domain || hostname.endsWith('.' + domain)
    );
  }

  // 检查Cookie
  checkCookies() {
    this.log('检查Cookie...');
    const cookies = document.cookie;
    
    if (!cookies) {
      this.log('未找到任何Cookie');
      return false;
    }

    this.log(`找到Cookie: ${cookies.substring(0, 100)}...`);
    
    // 检查钉钉相关的Cookie
    const dingTalkCookies = [
      'dingtalk',
      '_tb_token_',
      'login',
      'account',
      'cna'
    ];

    const foundCookies = dingTalkCookies.filter(cookieName => 
      cookies.includes(cookieName)
    );

    this.log(`找到钉钉相关Cookie: ${foundCookies.join(', ')}`);
    return foundCookies.length > 0;
  }

  // 测试API调用
  async testAPICall(endpoint, method = 'GET') {
    this.log(`测试API调用: ${method} ${endpoint}`);
    
    try {
      const options = {
        method: method,
        credentials: 'include',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        }
      };

      // 如果是POST请求，添加空的body
      if (method === 'POST') {
        options.body = JSON.stringify({});
      }

      const response = await fetch(`${this.baseUrl}${endpoint}`, options);
      
      this.log(`API响应: ${response.status} ${response.statusText}`);
      
      if (response.ok) {
        const data = await response.json();
        this.log(`API数据: ${JSON.stringify(data, null, 2)}`);
        return { success: true, data };
      } else {
        const errorText = await response.text();
        this.log(`API错误: ${errorText}`);
        return { success: false, status: response.status, error: errorText };
      }
      
    } catch (error) {
      this.log(`API异常: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  // 完整的认证测试
  async runFullTest() {
    this.log('=== 开始完整认证测试 ===');
    
    // 1. 检查域名
    const isDingTalkDomain = this.isDingTalkDomain();
    this.log(`钉钉域名检查: ${isDingTalkDomain ? '✅ 通过' : '❌ 失败'}`);
    
    if (!isDingTalkDomain) {
      this.log('当前不在钉钉域名，测试结束');
      return;
    }

    // 2. 检查Cookie
    const hasCookies = this.checkCookies();
    this.log(`Cookie检查: ${hasCookies ? '✅ 通过' : '❌ 失败'}`);

    // 3. 测试API端点
    const apiEndpoints = [
      { endpoint: '/api/users/getUserInfo', method: 'GET' },
      { endpoint: '/api/users/getUserInfo', method: 'POST' },
      { endpoint: '/api/user/info', method: 'GET' },
      { endpoint: '/api/user/info', method: 'POST' }
    ];

    this.log('开始测试API端点...');
    
    for (const config of apiEndpoints) {
      const result = await this.testAPICall(config.endpoint, config.method);
      
      if (result.success) {
        this.log(`✅ ${config.method} ${config.endpoint} - 成功`);
        if (result.data && (result.data.name || result.data.userId)) {
          this.log('🎉 找到用户信息！');
          this.userInfo = result.data;
          this.isAuthenticated = true;
          break;
        }
      } else {
        const status = result.status ? `(${result.status})` : '';
        this.log(`❌ ${config.method} ${config.endpoint} - 失败 ${status}`);
      }
      
      // 添加延迟避免请求过快
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // 4. 总结结果
    this.log('=== 测试结果总结 ===');
    this.log(`认证状态: ${this.isAuthenticated ? '✅ 已认证' : '❌ 未认证'}`);
    
    if (this.userInfo) {
      this.log('用户信息:');
      this.log(JSON.stringify(this.userInfo, null, 2));
    } else if (hasCookies) {
      this.log('虽然有Cookie但无法获取用户信息，将使用基础认证状态');
      this.userInfo = {
        name: '钉钉用户',
        userId: `dingtalk_user_${Date.now()}`,
        loginTime: new Date().toLocaleString(),
        source: 'cookie_auth'
      };
      this.isAuthenticated = true;
      this.log('✅ 使用基础认证状态');
    }

    this.log('=== 完整认证测试结束 ===');
    
    return {
      isAuthenticated: this.isAuthenticated,
      userInfo: this.userInfo,
      isDingTalkDomain,
      hasCookies
    };
  }
}

// 创建测试实例并运行测试
const tester = new DingTalkAuthTester();

// 如果在钉钉域名下，自动运行测试
if (tester.isDingTalkDomain()) {
  console.log('检测到钉钉域名，开始自动测试...');
  tester.runFullTest().then(result => {
    console.log('测试完成，结果:', result);
  });
} else {
  console.log('当前不在钉钉域名，请在钉钉网站上运行此测试');
  console.log('建议访问: https://docs.dingtalk.com');
}

// 导出测试器供手动调用
window.dingTalkTester = tester;
console.log('测试器已挂载到 window.dingTalkTester，可手动调用 dingTalkTester.runFullTest()');
